<!DOCTYPE html>
<html>
<head>
    <title>创建基本游戏资源</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        button { padding: 10px 20px; margin: 5px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>游戏资源生成器</h1>
    
    <div class="section">
        <h2>背景图片 (800x600)</h2>
        <canvas id="backgroundCanvas" width="800" height="600"></canvas>
        <br>
        <button onclick="downloadCanvas('backgroundCanvas', 'background.png')">下载背景图片</button>
    </div>
    
    <div class="section">
        <h2>Logo图片 (400x200)</h2>
        <canvas id="logoCanvas" width="400" height="200"></canvas>
        <br>
        <button onclick="downloadCanvas('logoCanvas', 'logo.png')">下载Logo图片</button>
    </div>
    
    <div class="section">
        <h2>表情符号图集 (512x320)</h2>
        <canvas id="emojiCanvas" width="512" height="320"></canvas>
        <br>
        <button onclick="downloadCanvas('emojiCanvas', 'emojis.png')">下载表情符号图集</button>
    </div>

    <script>
        // 创建背景图片
        function createBackground() {
            const canvas = document.getElementById('backgroundCanvas');
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 600);
            gradient.addColorStop(0, '#008eb0');
            gradient.addColorStop(0.5, '#0099cc');
            gradient.addColorStop(1, '#006699');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 600);
            
            // 添加装饰性的圆圈
            ctx.globalAlpha = 0.1;
            for (let i = 0; i < 30; i++) {
                ctx.beginPath();
                ctx.arc(
                    Math.random() * 800, 
                    Math.random() * 600, 
                    Math.random() * 80 + 20, 
                    0, 
                    Math.PI * 2
                );
                ctx.fillStyle = '#ffffff';
                ctx.fill();
            }
            
            // 添加网格效果
            ctx.globalAlpha = 0.05;
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 1;
            for (let x = 0; x < 800; x += 40) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, 600);
                ctx.stroke();
            }
            for (let y = 0; y < 600; y += 40) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(800, y);
                ctx.stroke();
            }
        }

        // 创建Logo
        function createLogo() {
            const canvas = document.getElementById('logoCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 400, 200);
            
            // 背景
            const gradient = ctx.createLinearGradient(0, 0, 400, 200);
            gradient.addColorStop(0, 'rgba(0, 142, 176, 0.8)');
            gradient.addColorStop(1, 'rgba(0, 102, 153, 0.8)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 400, 200);
            
            // 设置文字样式
            ctx.font = 'bold 36px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 添加阴影
            ctx.shadowColor = '#000000';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // 绘制文字
            ctx.fillStyle = '#ffffff';
            ctx.fillText('🎮 EMOJI MATCH 🎯', 200, 100);
        }

        // 创建表情符号图集
        function createEmojiAtlas() {
            const canvas = document.getElementById('emojiCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 512, 320);
            
            // 背景色
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(0, 0, 512, 320);
            
            // 表情符号列表（使用简单的几何图形代替真实emoji）
            const colors = [
                '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', 
                '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
                '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
                '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF',
                '#FAD7A0', '#E8DAEF', '#D1F2EB', '#FADBD8', '#D6EAF8',
                '#EBDEF0', '#D0ECE7', '#FCF3CF', '#F4D1AE', '#D5DBDB',
                '#EAEDED', '#F8F9F9', '#FDFEFE', '#F7F9F9', '#FAFAFA',
                '#F4F6F6', '#EBEDEF', '#E5E7E9', '#D0D3D4', '#BDC3C7'
            ];
            
            // 绘制8x5网格的彩色圆圈（代表表情符号）
            let index = 0;
            for (let row = 0; row < 5; row++) {
                for (let col = 0; col < 8; col++) {
                    if (index < colors.length) {
                        const x = col * 64 + 32;
                        const y = row * 64 + 32;
                        
                        // 绘制圆形背景
                        ctx.beginPath();
                        ctx.arc(x, y, 25, 0, Math.PI * 2);
                        ctx.fillStyle = colors[index];
                        ctx.fill();
                        
                        // 添加边框
                        ctx.strokeStyle = '#333';
                        ctx.lineWidth = 2;
                        ctx.stroke();
                        
                        // 添加简单的表情特征
                        ctx.fillStyle = '#333';
                        // 眼睛
                        ctx.beginPath();
                        ctx.arc(x - 8, y - 5, 3, 0, Math.PI * 2);
                        ctx.fill();
                        ctx.beginPath();
                        ctx.arc(x + 8, y - 5, 3, 0, Math.PI * 2);
                        ctx.fill();
                        
                        // 嘴巴
                        ctx.beginPath();
                        ctx.arc(x, y + 5, 8, 0, Math.PI);
                        ctx.stroke();
                        
                        index++;
                    }
                }
            }
        }

        // 下载画布内容
        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // 页面加载时创建所有图片
        window.onload = function() {
            createBackground();
            createLogo();
            createEmojiAtlas();
        };
    </script>
</body>
</html>
