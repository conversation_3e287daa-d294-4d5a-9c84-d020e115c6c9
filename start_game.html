<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji Match - 游戏启动器</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }

        h1 {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .button {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .button.secondary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }

        .button.warning {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }

        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
        }

        .status.success {
            background: rgba(76, 175, 80, 0.3);
            border: 1px solid rgba(76, 175, 80, 0.5);
        }

        .status.warning {
            background: rgba(255, 152, 0, 0.3);
            border: 1px solid rgba(255, 152, 0, 0.5);
        }

        .instructions {
            text-align: left;
            margin: 20px 0;
        }

        .instructions ol {
            padding-left: 20px;
        }

        .instructions li {
            margin: 10px 0;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 Emoji Match Game 🎯</h1>
        
        <div class="card">
            <h2>🚀 快速开始</h2>
            <p>选择下面的选项来开始你的游戏体验：</p>
            
            <a href="index.html" class="button">🎮 直接开始游戏</a>
            <a href="create_basic_assets.html" class="button secondary">🎨 生成游戏资源</a>
        </div>

        <div class="card">
            <h2>📋 游戏状态检查</h2>
            <div id="gameStatus">
                <p>正在检查游戏资源...</p>
            </div>
        </div>

        <div class="card">
            <h2>🛠️ 资源生成指南</h2>
            <div class="instructions">
                <p><strong>如果这是你第一次运行游戏，请按照以下步骤生成资源：</strong></p>
                <ol>
                    <li>点击上方的 <strong>"🎨 生成游戏资源"</strong> 按钮</li>
                    <li>在新页面中，点击各个下载按钮获取图片文件</li>
                    <li>将下载的文件保存到 <code>assets/images/</code> 文件夹中</li>
                    <li>（可选）生成音频文件并保存到 <code>assets/sounds/</code> 文件夹中</li>
                    <li>返回此页面，点击 <strong>"🎮 直接开始游戏"</strong></li>
                </ol>
            </div>
        </div>

        <div class="card">
            <h2>🎯 游戏规则</h2>
            <div class="instructions">
                <ul style="list-style: none; padding: 0;">
                    <li>🎯 <strong>目标</strong>: 在30秒内找到尽可能多的匹配表情符号对</li>
                    <li>🖱️ <strong>操作</strong>: 点击两个相同的表情符号来匹配它们</li>
                    <li>⭐ <strong>计分</strong>: 每成功匹配一对获得1分</li>
                    <li>🏆 <strong>挑战</strong>: 挑战你的最高分记录！</li>
                </ul>
            </div>
        </div>

        <div class="card">
            <h2>🔧 技术信息</h2>
            <p>本游戏基于 Phaser 3 引擎开发，所有资源都从本地加载，无需网络连接。</p>
            <p>支持现代浏览器：Chrome 60+, Firefox 55+, Safari 11+, Edge 79+</p>
        </div>
    </div>

    <script>
        // 检查游戏资源状态
        function checkGameAssets() {
            const statusDiv = document.getElementById('gameStatus');
            const requiredAssets = [
                'assets/images/background.png',
                'assets/images/logo.png',
                'assets/images/emojis.png',
                'assets/images/emojis.json'
            ];

            let statusHTML = '<h3>资源检查结果：</h3>';
            let allAssetsReady = true;

            // 注意：由于浏览器安全限制，我们无法直接检查本地文件是否存在
            // 这里提供一个基本的状态显示
            statusHTML += '<div class="status warning">';
            statusHTML += '<p><strong>⚠️ 注意</strong>: 由于浏览器安全限制，无法自动检查本地文件。</p>';
            statusHTML += '<p>如果游戏无法正常显示图片，请使用资源生成工具创建所需文件。</p>';
            statusHTML += '</div>';

            statusHTML += '<div class="status success">';
            statusHTML += '<p><strong>✅ 游戏代码</strong>: 所有JavaScript文件已就绪</p>';
            statusHTML += '<p><strong>✅ 配置文件</strong>: 表情符号配置文件已创建</p>';
            statusHTML += '</div>';

            statusDiv.innerHTML = statusHTML;
        }

        // 页面加载时检查资源
        window.addEventListener('load', checkGameAssets);
    </script>
</body>
</html>
