export default class Preloader extends Phaser.Scene
{
    constructor ()
    {
        super('Preloader');

        this.loadText;
    }

    preload ()
    {
        // 从本地加载资源，不使用CDN
        this.loadText = this.add.text(400, 360, '加载中...', { fontFamily: 'Arial', fontSize: 64, color: '#e3f2ed' });

        this.loadText.setOrigin(0.5);
        this.loadText.setStroke('#203c5b', 6);
        this.loadText.setShadow(2, 2, '#2d2d2d', 4, true, false);

        // 加载本地图片资源
        this.load.setPath('assets/images/');
        this.load.image('background', 'background.png');
        this.load.image('logo', 'logo.png');
        this.load.atlas('emojis', 'emojis.png', 'emojis.json');

        // 加载本地音频资源（可选）
        this.load.setPath('assets/sounds/');

        // 使用错误处理来加载音频，如果文件不存在也不会报错
        this.load.on('loaderror', (file) => {
            console.log('音频文件加载失败，游戏将在静音模式下运行:', file.key);
        });

        this.load.audio('music', ['music.mp3', 'music.ogg', 'music.wav']);
        this.load.audio('countdown', ['countdown.mp3', 'countdown.ogg', 'countdown.wav']);
        this.load.audio('match', ['match.mp3', 'match.ogg', 'match.wav']);
    }

    create ()
    {
        if (this.sound.locked)
        {
            this.loadText.setText('Click to Start');

            this.input.once('pointerdown', () => {

                this.scene.start('MainMenu');

            });
        }
        else
        {
            this.scene.start('MainMenu');
        }
    }
}