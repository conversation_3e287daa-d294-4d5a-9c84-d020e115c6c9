<!DOCTYPE html>
<html>
<head>
    <title>创建游戏图片资源</title>
</head>
<body>
    <canvas id="backgroundCanvas" width="800" height="600" style="border: 1px solid black;"></canvas>
    <br><br>
    <canvas id="logoCanvas" width="400" height="200" style="border: 1px solid black;"></canvas>
    <br><br>
    <canvas id="emojiCanvas" width="512" height="512" style="border: 1px solid black;"></canvas>
    <br><br>
    <button onclick="downloadBackground()">下载背景图片</button>
    <button onclick="downloadLogo()">下载Logo图片</button>
    <button onclick="downloadEmojis()">下载表情符号图集</button>

    <script>
        // 创建背景图片
        function createBackground() {
            const canvas = document.getElementById('backgroundCanvas');
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 800, 600);
            gradient.addColorStop(0, '#008eb0');
            gradient.addColorStop(0.5, '#0099cc');
            gradient.addColorStop(1, '#006699');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, 800, 600);
            
            // 添加一些装饰性的圆圈
            ctx.globalAlpha = 0.1;
            for (let i = 0; i < 20; i++) {
                ctx.beginPath();
                ctx.arc(Math.random() * 800, Math.random() * 600, Math.random() * 50 + 20, 0, Math.PI * 2);
                ctx.fillStyle = '#ffffff';
                ctx.fill();
            }
        }

        // 创建Logo
        function createLogo() {
            const canvas = document.getElementById('logoCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 400, 200);
            
            // 设置文字样式
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 添加阴影
            ctx.shadowColor = '#000000';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 2;
            ctx.shadowOffsetY = 2;
            
            // 绘制文字
            ctx.fillStyle = '#ffffff';
            ctx.fillText('🎮 EMOJI MATCH 🎯', 200, 100);
        }

        // 创建表情符号图集
        function createEmojiAtlas() {
            const canvas = document.getElementById('emojiCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 512, 512);
            
            // 表情符号列表
            const emojis = [
                '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
                '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
                '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
                '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
                '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣'
            ];
            
            // 设置字体大小
            ctx.font = '60px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 绘制8x5网格的表情符号
            let index = 0;
            for (let row = 0; row < 5; row++) {
                for (let col = 0; col < 8; col++) {
                    if (index < emojis.length) {
                        const x = col * 64 + 32;
                        const y = row * 64 + 32;
                        ctx.fillText(emojis[index], x, y);
                        index++;
                    }
                }
            }
        }

        // 下载函数
        function downloadBackground() {
            const canvas = document.getElementById('backgroundCanvas');
            const link = document.createElement('a');
            link.download = 'background.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadLogo() {
            const canvas = document.getElementById('logoCanvas');
            const link = document.createElement('a');
            link.download = 'logo.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        function downloadEmojis() {
            const canvas = document.getElementById('emojiCanvas');
            const link = document.createElement('a');
            link.download = 'emojis.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // 页面加载时创建图片
        window.onload = function() {
            createBackground();
            createLogo();
            createEmojiAtlas();
        };
    </script>
</body>
</html>
