<!DOCTYPE html>
<html>
<head>
    <title>创建游戏音频资源</title>
</head>
<body>
    <h1>游戏音频生成器</h1>
    <p>点击按钮生成并下载音频文件：</p>
    
    <button onclick="generateMatchSound()">生成匹配音效</button>
    <button onclick="generateCountdownSound()">生成倒计时音效</button>
    <button onclick="generateBackgroundMusic()">生成背景音乐</button>
    
    <br><br>
    <audio id="audioPlayer" controls style="width: 300px;"></audio>
    
    <script>
        // 音频上下文
        let audioContext;
        
        // 初始化音频上下文
        function initAudioContext() {
            if (!audioContext) {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
            return audioContext;
        }
        
        // 生成匹配成功音效
        function generateMatchSound() {
            const ctx = initAudioContext();
            const duration = 0.5;
            const sampleRate = ctx.sampleRate;
            const buffer = ctx.createBuffer(1, duration * sampleRate, sampleRate);
            const data = buffer.getChannelData(0);
            
            // 生成愉快的上升音调
            for (let i = 0; i < data.length; i++) {
                const t = i / sampleRate;
                const frequency = 440 + (t * 200); // 从440Hz上升到640Hz
                const envelope = Math.exp(-t * 3); // 衰减包络
                data[i] = Math.sin(2 * Math.PI * frequency * t) * envelope * 0.3;
            }
            
            playAndDownload(buffer, 'match.mp3');
        }
        
        // 生成倒计时音效
        function generateCountdownSound() {
            const ctx = initAudioContext();
            const duration = 3.0;
            const sampleRate = ctx.sampleRate;
            const buffer = ctx.createBuffer(1, duration * sampleRate, sampleRate);
            const data = buffer.getChannelData(0);
            
            // 生成紧张的倒计时音效
            for (let i = 0; i < data.length; i++) {
                const t = i / sampleRate;
                let sound = 0;
                
                // 每秒一个哔声
                if (t < 1 || (t >= 1 && t < 2) || (t >= 2 && t < 3)) {
                    const beepTime = t % 1;
                    if (beepTime < 0.1) {
                        const frequency = 800;
                        const envelope = Math.exp(-beepTime * 20);
                        sound = Math.sin(2 * Math.PI * frequency * beepTime) * envelope * 0.4;
                    }
                }
                
                data[i] = sound;
            }
            
            playAndDownload(buffer, 'countdown.mp3');
        }
        
        // 生成背景音乐
        function generateBackgroundMusic() {
            const ctx = initAudioContext();
            const duration = 10.0; // 10秒循环
            const sampleRate = ctx.sampleRate;
            const buffer = ctx.createBuffer(1, duration * sampleRate, sampleRate);
            const data = buffer.getChannelData(0);
            
            // 生成简单的背景音乐
            const notes = [261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88]; // C大调音阶
            
            for (let i = 0; i < data.length; i++) {
                const t = i / sampleRate;
                let sound = 0;
                
                // 主旋律
                const noteIndex = Math.floor((t * 2) % notes.length);
                const frequency = notes[noteIndex];
                const envelope = 0.5 + 0.3 * Math.sin(t * 0.5);
                sound += Math.sin(2 * Math.PI * frequency * t) * envelope * 0.1;
                
                // 和声
                const harmonyFreq = frequency * 1.5;
                sound += Math.sin(2 * Math.PI * harmonyFreq * t) * envelope * 0.05;
                
                data[i] = sound;
            }
            
            playAndDownload(buffer, 'music.mp3');
        }
        
        // 播放并下载音频
        function playAndDownload(buffer, filename) {
            const ctx = audioContext;
            
            // 播放音频
            const source = ctx.createBufferSource();
            source.buffer = buffer;
            source.connect(ctx.destination);
            source.start();
            
            // 转换为WAV格式并下载
            const wav = bufferToWav(buffer);
            const blob = new Blob([wav], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            
            // 设置音频播放器
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.src = url;
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = filename.replace('.mp3', '.wav'); // 实际是WAV格式
            link.click();
            
            // 清理URL
            setTimeout(() => URL.revokeObjectURL(url), 1000);
        }
        
        // 将AudioBuffer转换为WAV格式
        function bufferToWav(buffer) {
            const length = buffer.length;
            const arrayBuffer = new ArrayBuffer(44 + length * 2);
            const view = new DataView(arrayBuffer);
            const data = buffer.getChannelData(0);
            
            // WAV文件头
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, buffer.sampleRate, true);
            view.setUint32(28, buffer.sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * 2, true);
            
            // 音频数据
            let offset = 44;
            for (let i = 0; i < length; i++) {
                const sample = Math.max(-1, Math.min(1, data[i]));
                view.setInt16(offset, sample * 0x7FFF, true);
                offset += 2;
            }
            
            return arrayBuffer;
        }
        
        // 页面加载时初始化
        window.addEventListener('load', () => {
            // 添加用户交互以启用音频上下文
            document.addEventListener('click', initAudioContext, { once: true });
        });
    </script>
</body>
</html>
