# 🎮 Emoji Match Game - 表情符号匹配游戏

一个基于Phaser 3的本地表情符号匹配游戏，所有资源都从本地加载，无需CDN。

## 🎯 游戏说明

- **目标**: 在30秒内找到尽可能多的匹配表情符号对
- **操作**: 点击两个相同的表情符号来匹配它们
- **计分**: 每成功匹配一对获得1分
- **挑战**: 挑战你的最高分记录！

## 🚀 快速开始

1. **直接运行**: 打开 `index.html` 文件即可开始游戏
2. **生成资源**: 如果需要自定义资源，请参考下面的资源生成说明

## 📁 项目结构

```
emojiMatch/
├── index.html              # 主游戏页面
├── main.js                 # 游戏主入口
├── Boot.js                 # 启动场景
├── Preloader.js            # 资源预加载场景
├── MainMenu.js             # 主菜单场景
├── Game.js                 # 游戏主场景
├── assets/                 # 游戏资源文件夹
│   ├── images/             # 图片资源
│   │   ├── background.png  # 背景图片
│   │   ├── logo.png        # Logo图片
│   │   ├── emojis.png      # 表情符号图集
│   │   ├── emojis.json     # 表情符号图集配置
│   │   └── create_images.html # 图片生成工具
│   └── sounds/             # 音频资源
│       ├── music.mp3       # 背景音乐
│       ├── countdown.mp3   # 倒计时音效
│       ├── match.mp3       # 匹配成功音效
│       ├── create_sounds.html # 音频生成工具
│       └── README.md       # 音频说明
├── create_basic_assets.html # 基础资源生成工具
└── README.md               # 项目说明文件
```

## 🎨 资源生成

### 生成图片资源

1. 打开 `create_basic_assets.html`
2. 页面会自动生成预览图片
3. 点击相应按钮下载图片文件
4. 将下载的文件放入 `assets/images/` 文件夹

### 生成音频资源

1. 打开 `assets/sounds/create_sounds.html`
2. 点击按钮生成音频文件
3. 将下载的文件重命名并放入 `assets/sounds/` 文件夹

## 🔧 技术特性

- **本地资源**: 所有资源从本地加载，无需网络连接
- **响应式设计**: 支持不同屏幕尺寸
- **音频容错**: 即使没有音频文件也能正常运行
- **现代化界面**: 使用CSS3动画和渐变效果
- **Phaser 3引擎**: 基于强大的HTML5游戏引擎

## 🎵 音频支持

游戏支持以下音频格式：
- MP3 (推荐)
- OGG (备用)
- WAV (备用)

如果音频文件不存在，游戏会在静音模式下运行。

## 🎮 游戏控制

- **鼠标点击**: 选择表情符号
- **触摸**: 支持移动设备触摸操作
- **键盘**: 无需键盘操作

## 🌟 特色功能

- **渐变动画**: 炫酷的标题渐变效果
- **加载动画**: 优雅的加载屏幕
- **音效反馈**: 匹配成功和倒计时音效
- **分数记录**: 自动保存最高分
- **视觉反馈**: 选择和匹配的视觉提示

## 🔄 游戏流程

1. **启动**: 显示加载屏幕
2. **主菜单**: 显示最高分和开始按钮
3. **游戏**: 30秒匹配挑战
4. **结束**: 显示匹配位置并返回主菜单

## 🛠️ 自定义修改

### 修改游戏时间
在 `Game.js` 第91行修改：
```javascript
this.timer = this.time.addEvent({ delay: 30000, ... }); // 30秒
```

### 修改网格大小
在 `Game.js` 第42-49行修改网格配置

### 修改表情符号数量
在 `Game.js` 第175行修改：
```javascript
let frames = Phaser.Utils.Array.NumberArray(1, 40); // 40个表情符号
```

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 🎉 开始游戏

直接打开 `index.html` 文件，享受游戏吧！🎮
