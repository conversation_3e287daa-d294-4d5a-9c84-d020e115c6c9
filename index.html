<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji Match Game - 表情符号匹配游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .game-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            backdrop-filter: blur(4px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        #phaser-example {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .game-info {
            text-align: center;
            color: white;
            margin-bottom: 15px;
        }

        .game-info h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .game-info p {
            margin: 5px 0;
            font-size: 1.1em;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            transition: opacity 0.5s ease-out;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .loading-text {
            color: white;
            font-size: 2em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-top: 5px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .controls-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            color: white;
            font-size: 0.9em;
            text-align: left;
        }

        .controls-info h3 {
            margin: 0 0 10px 0;
            color: #4ecdc4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .controls-info ul {
            margin: 0;
            padding-left: 20px;
        }

        .controls-info li {
            margin: 5px 0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 900px) {
            .game-container {
                margin: 10px;
                padding: 15px;
            }
            
            .game-info h1 {
                font-size: 2em;
            }
            
            #phaser-example {
                transform: scale(0.8);
                transform-origin: center;
            }
        }
    </style>
</head>
<body>
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-text">加载游戏资源中...</div>
        <div class="loading-spinner"></div>
    </div>

    <div class="game-container">
        <div class="game-info">
            <h1>🎮 Emoji Match 🎯</h1>
            <p>🕐 在30秒内找到匹配的表情符号对！</p>
            <p>🎯 点击两个相同的表情符号来匹配它们</p>
        </div>
        
        <div id="phaser-example"></div>
        
        <div class="controls-info">
            <h3>🎮 游戏说明：</h3>
            <ul>
                <li>🖱️ 点击表情符号来选择</li>
                <li>🎯 找到两个相同的表情符号</li>
                <li>⏰ 在时间用完前尽可能多地匹配</li>
                <li>🏆 挑战你的最高分记录！</li>
            </ul>
        </div>
    </div>

    <!-- Phaser 3 游戏引擎 -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- 游戏脚本 -->
    <script type="module" src="main.js"></script>

    <script>
        // 隐藏加载屏幕
        window.addEventListener('load', function() {
            setTimeout(function() {
                const loadingScreen = document.getElementById('loadingScreen');
                loadingScreen.classList.add('hidden');
                setTimeout(function() {
                    loadingScreen.style.display = 'none';
                }, 500);
            }, 1000);
        });

        // 防止右键菜单
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // 防止选择文本
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
